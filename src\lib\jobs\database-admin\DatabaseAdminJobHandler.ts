import { BaseJobHandler } from "../base/BaseJobHandler";
import type {
  <PERSON><PERSON><PERSON><PERSON>,
  JobExecutionContext,
  DatabaseOperationResult,
} from "../types";
import type {
  JobDefinition,
  DatabaseOperation,
  VisualQueryConfig,
} from "../../jobManager";
import { logger } from "../../jobManager";
import type { Pool } from "mysql2/promise";

/**
 * Job handler for database administration operations
 */
export class DatabaseAdminJobHandler extends BaseJobHandler {
  public readonly jobType = "database_admin";

  /**
   * Execute database administration job
   */
  public async execute(context: JobExecutionContext): Promise<JobResult> {
    const { jobDefinition } = context;
    const dbConfig = jobDefinition.dataSource.database_admin;

    if (!dbConfig) {
      throw new Error(
        "Database admin configuration is required for database_admin data source"
      );
    }

    // Validate configuration
    this.validateDatabaseAdminConfig(dbConfig);

    const startTime = await this.logOperationStart(
      context,
      "Database administration operations",
      `${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`
    );

    try {
      // Import mysql2 for database connection
      const mysql = await import("mysql2/promise");

      await context.addLog("🔌 Establishing database connection...");

      // Create connection configuration
      const connectionConfig = {
        host: dbConfig.host,
        port: dbConfig.port,
        user: dbConfig.username,
        password: dbConfig.password,
        database: dbConfig.database,
        waitForConnections: true,
        connectionLimit: dbConfig.connectionLimit || 10,
        queueLimit: 0,
        multipleStatements: true, // Enable multiple statements
      };

      // Create connection pool
      const pool = mysql.createPool(connectionConfig);

      // Test the connection
      const testConnection = await pool.getConnection();
      testConnection.release();

      await context.addLog("✅ Connected to MySQL database successfully");
      await context.addLog(
        `📊 Connection pool configured with ${connectionConfig.connectionLimit} max connections`
      );

      let results: DatabaseOperationResult[] = [];

      // Execute operations based on configuration
      if (dbConfig.operations && dbConfig.operations.length > 0) {
        results = await this.executeMultiStepOperations(
          context,
          pool,
          dbConfig.operations
        );
      } else if (dbConfig.rawSql) {
        results = await this.executeRawSqlOperations(
          context,
          pool,
          dbConfig.rawSql
        );
      } else if (dbConfig.visualQuery) {
        results = await this.executeVisualQueryOperations(
          context,
          pool,
          dbConfig.visualQuery
        );
      } else {
        throw new Error(
          "No operations, raw SQL, or visual query specified in database admin configuration"
        );
      }

      // Calculate summary statistics
      const totalOperations = results.length;
      const successfulOperations = results.filter((r) => r.success).length;
      const failedOperations = results.filter((r) => !r.success).length;
      const totalExecutionTime = results.reduce(
        (sum, result) => sum + (result.executionTimeMs || 0),
        0
      );

      await context.addLog(
        "🎉 Database administration completed successfully!"
      );
      await context.addLog(
        `📊 Summary: ${totalOperations} operations executed, ${successfulOperations} successful, ${failedOperations} failed`
      );
      await context.addLog(
        `⏱️ Total execution time: ${totalExecutionTime}ms (avg: ${Math.round(
          totalExecutionTime / totalOperations
        )}ms per operation)`
      );

      await this.logOperationComplete(
        context,
        "Database administration",
        startTime,
        `${successfulOperations}/${totalOperations} operations successful`
      );

      logger.info(
        `Database admin operations executed successfully for job ${context.jobId}`,
        {
          jobId: context.jobId,
          operationCount: totalOperations,
          successfulOperations,
          failedOperations,
          totalExecutionTimeMs: totalExecutionTime,
          host: dbConfig.host,
          database: dbConfig.database,
        }
      );

      // Close the pool
      await context.addLog("🔌 Closing database connection pool...");
      await pool.end();
      await context.addLog("✅ Database connection pool closed successfully");

      return this.createJobResult(successfulOperations, {
        type: "database_admin",
        operationCount: totalOperations,
        successfulOperations,
        failedOperations,
        totalExecutionTimeMs: totalExecutionTime,
        results: results,
        message: "Database administration operations completed successfully",
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown database admin error";

      await context.addLog(
        `💥 Database administration failed: ${errorMessage}`
      );

      // Log additional error details if available
      if (error instanceof Error && error.stack) {
        await context.addLog(`🔍 Error details: ${error.stack.split("\n")[0]}`);
      }

      logger.error(
        `Database admin operations failed for job ${context.jobId}`,
        {
          jobId: context.jobId,
          error: errorMessage,
          host: dbConfig.host,
          database: dbConfig.database,
          stack: error instanceof Error ? error.stack : undefined,
        }
      );

      throw new Error(`Database administration failed: ${errorMessage}`);
    }
  }

  /**
   * Execute multi-step database operations
   */
  private async executeMultiStepOperations(
    context: JobExecutionContext,
    pool: Pool,
    operations: DatabaseOperation[]
  ): Promise<DatabaseOperationResult[]> {
    const totalOperations = operations.length;
    await context.addLog(
      `🔄 Executing ${totalOperations} database operations in sequence...`
    );

    // Sort operations by dependencies
    const sortedOperations = this.sortOperationsByDependencies(operations);

    await context.addLog(
      `📋 Operation execution order: ${sortedOperations
        .map((op) => op.name)
        .join(" → ")}`
    );

    const results: DatabaseOperationResult[] = [];

    // Start transaction for multi-step operations
    const connection = await pool.getConnection();
    await connection.beginTransaction();
    await context.addLog("🔒 Transaction started for multi-step operations");

    try {
      for (let i = 0; i < sortedOperations.length; i++) {
        await context.checkCancellation();

        const operation = sortedOperations[i];
        const operationStartTime = Date.now();

        await context.addLog(
          `⚡ [${i + 1}/${totalOperations}] Starting operation: "${
            operation.name
          }" (${operation.type})`
        );

        try {
          const [result] = await connection.execute(operation.sql);
          const executionTime = Date.now() - operationStartTime;

          results.push({
            operationId: operation.id,
            operationName: operation.name,
            type: operation.type,
            success: true,
            result: result,
            executionTimeMs: executionTime,
          });

          await context.addLog(
            `✅ Operation "${operation.name}" completed successfully in ${executionTime}ms`
          );
        } catch (operationError) {
          const executionTime = Date.now() - operationStartTime;
          const errorMessage =
            operationError instanceof Error
              ? operationError.message
              : "Unknown error";

          results.push({
            operationId: operation.id,
            operationName: operation.name,
            type: operation.type,
            success: false,
            error: errorMessage,
            executionTimeMs: executionTime,
          });

          await context.addLog(
            `❌ Operation "${operation.name}" failed after ${executionTime}ms: ${errorMessage}`
          );

          if (!operation.continueOnError) {
            await connection.rollback();
            await context.addLog(
              "🔄 Transaction rolled back due to operation failure"
            );
            throw new Error(
              `Operation "${operation.name}" failed: ${errorMessage}`
            );
          } else {
            await context.addLog(
              "⚠️ Continuing with next operation despite error (continueOnError=true)"
            );
          }
        }
      }

      // Commit transaction
      await connection.commit();
      await context.addLog(
        "✅ Transaction committed successfully - all operations completed"
      );

      return results;
    } catch (transactionError) {
      await connection.rollback();
      await context.addLog("❌ Transaction rolled back due to error");
      throw transactionError;
    } finally {
      connection.release();
    }
  }

  /**
   * Sort operations by dependencies using topological sort
   */
  private sortOperationsByDependencies(
    operations: DatabaseOperation[]
  ): DatabaseOperation[] {
    const sorted: DatabaseOperation[] = [];
    const visited = new Set<string>();
    const visiting = new Set<string>();

    const visit = (operation: DatabaseOperation) => {
      if (visiting.has(operation.id)) {
        throw new Error(
          `Circular dependency detected involving operation: ${operation.id}`
        );
      }

      if (visited.has(operation.id)) {
        return;
      }

      visiting.add(operation.id);

      // Visit dependencies first
      if (operation.dependsOn) {
        for (const depId of operation.dependsOn) {
          const dependency = operations.find((op) => op.id === depId);
          if (dependency) {
            visit(dependency);
          }
        }
      }

      visiting.delete(operation.id);
      visited.add(operation.id);
      sorted.push(operation);
    };

    for (const operation of operations) {
      if (!visited.has(operation.id)) {
        visit(operation);
      }
    }

    return sorted;
  }

  /**
   * Execute raw SQL operations
   */
  private async executeRawSqlOperations(
    context: JobExecutionContext,
    pool: Pool,
    rawSql: string
  ): Promise<DatabaseOperationResult[]> {
    await context.addLog("📜 Executing raw SQL operations...");

    const statements = rawSql
      .split(";")
      .map((stmt) => stmt.trim())
      .filter((stmt) => stmt.length > 0);

    const totalOperations = statements.length;
    await context.addLog(
      `📊 Found ${totalOperations} SQL statements to execute`
    );

    const results: DatabaseOperationResult[] = [];

    // Start transaction for raw SQL operations
    const connection = await pool.getConnection();
    await connection.beginTransaction();
    await context.addLog("🔒 Transaction started for raw SQL execution");

    try {
      for (let i = 0; i < statements.length; i++) {
        await context.checkCancellation();

        const statement = statements[i];
        const statementStartTime = Date.now();

        await context.addLog(
          `⚡ [${i + 1}/${totalOperations}] Executing SQL statement...`
        );

        try {
          const [result] = await connection.execute(statement);
          const executionTime = Date.now() - statementStartTime;

          results.push({
            statementIndex: i + 1,
            sql:
              statement.substring(0, 100) +
              (statement.length > 100 ? "..." : ""),
            success: true,
            result: result,
            executionTimeMs: executionTime,
          });

          await context.addLog(
            `✅ Statement ${i + 1} completed successfully in ${executionTime}ms`
          );
        } catch (statementError) {
          const executionTime = Date.now() - statementStartTime;
          const errorMessage =
            statementError instanceof Error
              ? statementError.message
              : "Unknown error";

          await context.addLog(
            `❌ Statement ${
              i + 1
            } failed after ${executionTime}ms: ${errorMessage}`
          );

          await connection.rollback();
          await context.addLog(
            "🔄 Transaction rolled back due to statement failure"
          );

          throw new Error(`SQL statement ${i + 1} failed: ${errorMessage}`);
        }
      }

      // Commit transaction
      await connection.commit();
      await context.addLog(
        "✅ Transaction committed successfully - all SQL statements executed"
      );

      return results;
    } catch (transactionError) {
      await connection.rollback();
      await context.addLog("❌ Transaction rolled back due to error");
      throw transactionError;
    } finally {
      connection.release();
    }
  }

  /**
   * Execute visual query operations
   */
  private async executeVisualQueryOperations(
    context: JobExecutionContext,
    pool: Pool,
    visualQuery: VisualQueryConfig
  ): Promise<DatabaseOperationResult[]> {
    await context.addLog("🎨 Executing visual query builder operations...");

    const visualQueryStartTime = Date.now();
    const generatedSQL = this.generateSQLFromVisualQuery(visualQuery);

    await context.addLog(
      `📝 Generated SQL from visual query: ${generatedSQL.substring(0, 200)}${
        generatedSQL.length > 200 ? "..." : ""
      }`
    );

    const results: DatabaseOperationResult[] = [];

    // Start transaction for visual query execution
    const connection = await pool.getConnection();
    await connection.beginTransaction();
    await context.addLog("🔒 Transaction started for visual query execution");

    try {
      await context.addLog("⚡ Executing generated SQL from visual query...");

      const [result] = await connection.execute(generatedSQL);
      const executionTime = Date.now() - visualQueryStartTime;

      results.push({
        type: "visual_query",
        operationType: visualQuery.operationType,
        sql: generatedSQL,
        success: true,
        result: result,
        executionTimeMs: executionTime,
      });

      await connection.commit();
      await context.addLog(
        `✅ Visual query executed successfully in ${executionTime}ms - transaction committed`
      );

      return results;
    } catch (visualQueryError) {
      const executionTime = Date.now() - visualQueryStartTime;
      const errorMessage =
        visualQueryError instanceof Error
          ? visualQueryError.message
          : "Unknown error";

      await connection.rollback();
      await context.addLog(
        `❌ Visual query failed after ${executionTime}ms: ${errorMessage} - transaction rolled back`
      );

      throw new Error(`Visual query execution failed: ${errorMessage}`);
    } finally {
      connection.release();
    }
  }

  /**
   * Generate SQL from visual query configuration
   */
  private generateSQLFromVisualQuery(visualQuery: VisualQueryConfig): string {
    // This is a simplified version - the full implementation would be more complex
    const { sourceTables, selectedColumns, targetTable, operationType } =
      visualQuery;

    let sql = "";

    if (operationType === "CREATE_TABLE_AS_SELECT") {
      sql += `CREATE TABLE ${targetTable} AS\n`;
    } else if (operationType === "DROP_TABLE") {
      return `DROP TABLE IF EXISTS ${targetTable}`;
    }

    // SELECT clause
    sql += "SELECT\n";
    const columnExpressions = selectedColumns.map((col) => {
      let expr = `${col.table}.${col.column}`;
      if (col.alias) {
        expr += ` AS ${col.alias}`;
      }
      return `    ${expr}`;
    });
    sql += columnExpressions.join(",\n") + "\n";

    // FROM clause
    sql += `FROM\n    ${sourceTables[0]}\n`;

    return sql;
  }

  /**
   * Validate database admin job configuration
   */
  public validateConfig(jobDef: JobDefinition): boolean {
    if (!super.validateConfig(jobDef)) {
      return false;
    }

    const dbConfig = jobDef.dataSource.database_admin;
    if (!dbConfig) {
      return false;
    }

    try {
      this.validateDatabaseAdminConfig(dbConfig);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get required permissions for database admin operations
   */
  public getRequiredPermissions(): string[] {
    return [
      "database:admin:read",
      "database:admin:write",
      "database:admin:ddl",
    ];
  }

  /**
   * Validate database admin configuration fields
   */
  private validateDatabaseAdminConfig(
    config: NonNullable<JobDefinition["dataSource"]["database_admin"]>
  ): void {
    this.validateRequiredFields(
      config as unknown as Record<string, unknown>,
      ["host", "port", "database", "username", "password", "operationMode"],
      "Database admin configuration"
    );

    // Validate that at least one operation type is specified
    const hasOperations = config.operations && config.operations.length > 0;
    const hasRawSql = config.rawSql && config.rawSql.trim().length > 0;
    const hasVisualQuery = config.visualQuery;

    if (!hasOperations && !hasRawSql && !hasVisualQuery) {
      throw new Error(
        "Database admin configuration must specify at least one of: operations, rawSql, or visualQuery"
      );
    }

    // Additional validations
    if (
      typeof config.port !== "number" ||
      config.port <= 0 ||
      config.port > 65535
    ) {
      throw new Error(
        "Database admin port must be a valid number between 1 and 65535"
      );
    }

    if (
      config.connectionLimit &&
      (config.connectionLimit <= 0 || config.connectionLimit > 100)
    ) {
      throw new Error(
        "Database admin connection limit must be between 1 and 100"
      );
    }
  }
}
