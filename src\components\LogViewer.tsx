"use client";

import React, {
  useEffect,
  useRef,
  useState,
  useMemo,
  useCallback,
} from "react";
import { Button, Input, Select, SelectItem, Chip } from "@heroui/react";
import { Search, Filter, Download, Eye, EyeOff } from "lucide-react";
import { VirtualizedLogList } from "./VirtualizedLogList";

interface LogEntry {
  id: string;
  message: string;
  timestamp: string;
  level: "info" | "warn" | "error" | "debug";
}

interface LogViewerProps {
  logs: string[];
  jobId: string;
  isFullscreen?: boolean;
}

// Removed animations for better performance

// Parse log entry to extract level and clean message
const parseLogEntry = (log: string, index: number): LogEntry => {
  const timestamp = log.match(/^\d{2}:\d{2}:\d{2}/)?.[0] || "";
  const message = log.replace(/^\d{2}:\d{2}:\d{2}\s*-?\s*/, "");

  // Determine log level based on content
  let level: LogEntry["level"] = "info";
  const lowerMessage = message.toLowerCase();

  if (
    lowerMessage.includes("error") ||
    lowerMessage.includes("failed") ||
    lowerMessage.includes("❌")
  ) {
    level = "error";
  } else if (
    lowerMessage.includes("warning") ||
    lowerMessage.includes("warn") ||
    lowerMessage.includes("⚠️")
  ) {
    level = "warn";
  } else if (lowerMessage.includes("debug")) {
    level = "debug";
  }

  return {
    id: `${index}-${timestamp}`,
    message,
    timestamp,
    level,
  };
};

// Memoized log entry component for performance
const LogEntryComponent = React.memo(
  ({ entry, searchTerm }: { entry: LogEntry; searchTerm: string }) => {
    const getLogColor = (level: LogEntry["level"]) => {
      switch (level) {
        case "error":
          return "text-red-600 dark:text-red-400";
        case "warn":
          return "text-yellow-600 dark:text-yellow-400";
        case "debug":
          return "text-gray-500 dark:text-gray-500";
        default:
          return "text-gray-700 dark:text-gray-300";
      }
    };

    const highlightText = (text: string, search: string) => {
      if (!search) return text;

      const regex = new RegExp(`(${search})`, "gi");
      const parts = text.split(regex);

      return parts.map((part, index) =>
        regex.test(part) ? (
          <span
            key={index}
            className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded"
          >
            {part}
          </span>
        ) : (
          part
        )
      );
    };

    return (
      <div
        className={`text-xs font-mono ${getLogColor(
          entry.level
        )} break-words py-1 leading-relaxed transition-colors duration-150 hover:bg-gray-300 dark:hover:bg-gray-800 px-2 rounded whitespace-pre-wrap`}
      >
        <span className="text-gray-400 dark:text-gray-600 mr-2">
          {entry.timestamp}
        </span>
        {highlightText(entry.message, searchTerm)}
      </div>
    );
  }
);

LogEntryComponent.displayName = "LogEntryComponent";

export const LogViewer = ({
  logs,
  jobId,
  isFullscreen = false,
}: LogViewerProps) => {
  const logContainerRef = useRef<HTMLDivElement>(null);
  const controlsRef = useRef<HTMLDivElement>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLevel, setSelectedLevel] = useState<string>("all");
  const [maxEntries, setMaxEntries] = useState(Infinity); // Default to show all entries
  const [autoScroll, setAutoScroll] = useState(true);
  const [showControls, setShowControls] = useState(false);
  const [useVirtualScrolling, setUseVirtualScrolling] = useState(true); // Default to enabled

  // Parse logs into structured entries
  const logEntries = useMemo(() => {
    return logs.map((log, index) => parseLogEntry(log, index));
  }, [logs]);

  // Automatically enable virtual scrolling for large log sets
  useEffect(() => {
    if (logs.length > 1000 && !useVirtualScrolling) {
      setUseVirtualScrolling(true);
    }
  }, [logs.length, useVirtualScrolling]);

  // Apply filtering and limiting
  const filteredLogs = useMemo(() => {
    let filtered = logEntries;

    // Filter by level
    if (selectedLevel !== "all") {
      filtered = filtered.filter((entry) => entry.level === selectedLevel);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter((entry) =>
        entry.message.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Limit entries (show most recent)
    if (filtered.length > maxEntries) {
      filtered = filtered.slice(-maxEntries);
    }

    return filtered;
  }, [logEntries, selectedLevel, searchTerm, maxEntries]);

  // Auto-scroll to bottom when new entries are added
  useEffect(() => {
    if (autoScroll && logContainerRef.current && filteredLogs.length > 0) {
      const container = logContainerRef.current;
      container.scrollTop = container.scrollHeight;
    }
  }, [filteredLogs, autoScroll]);

  // Handle click outside to close controls
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        showControls &&
        controlsRef.current &&
        !controlsRef.current.contains(event.target as Node)
      ) {
        // Check if the click is on the "Show Controls" button
        const target = event.target as Element;
        const isControlButton = target.closest("[data-controls-button]");
        if (!isControlButton) {
          setShowControls(false);
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showControls]);

  // Export logs functionality
  const handleExportLogs = useCallback(() => {
    const logText = logEntries
      .map(
        (entry) =>
          `${entry.timestamp} [${entry.level.toUpperCase()}] ${entry.message}`
      )
      .join("\n");

    const blob = new Blob([logText], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `job-${jobId}-logs.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [logEntries, jobId]);

  // Get level counts for display
  const levelCounts = useMemo(() => {
    return logEntries.reduce((acc, entry) => {
      acc[entry.level] = (acc[entry.level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [logEntries]);

  // Create a mapping for level display names
  const levelDisplayNames = useMemo(
    () => ({
      all: "All Levels",
      error: `Error (${levelCounts.error || 0})`,
      warn: `Warning (${levelCounts.warn || 0})`,
      info: `Info (${levelCounts.info || 0})`,
      debug: `Debug (${levelCounts.debug || 0})`,
    }),
    [levelCounts]
  );

  // Custom render function for selected value
  const renderValue = useCallback(
    (items: Array<{ key?: React.Key; textValue?: string }>) => {
      if (!items || items.length === 0) {
        return "Filter by level";
      }
      const selectedKey = Array.from(items)[0]?.key || selectedLevel;
      return (
        levelDisplayNames[selectedKey as keyof typeof levelDisplayNames] ||
        "Filter by level"
      );
    },
    [levelDisplayNames, selectedLevel]
  );

  return (
    <div className="h-full flex flex-col relative">
      {/* Main Control Toolbar */}
      <div className="flex items-center justify-between gap-3 mb-3">
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="flat"
            color="primary"
            onPress={() => setShowControls(!showControls)}
            startContent={<Filter className="w-4 h-4" />}
            className="font-medium transition-colors duration-200"
            data-controls-button
          >
            {showControls ? "Hide" : "Show"} Controls
          </Button>

          {logEntries.length > maxEntries && maxEntries !== Infinity && (
            <Chip size="sm" color="warning" variant="flat">
              Showing last {maxEntries} of {logEntries.length}
            </Chip>
          )}

          {useVirtualScrolling && (
            <Chip size="sm" color="primary" variant="flat">
              Virtual Scrolling
            </Chip>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="flat"
            color={autoScroll ? "success" : "default"}
            onPress={() => setAutoScroll(!autoScroll)}
            startContent={
              autoScroll ? (
                <Eye className="w-4 h-4" />
              ) : (
                <EyeOff className="w-4 h-4" />
              )
            }
            className="font-medium transition-colors duration-200"
          >
            Auto-scroll
          </Button>

          <Button
            size="sm"
            variant="flat"
            color="secondary"
            onPress={handleExportLogs}
            startContent={<Download className="w-4 h-4" />}
            className="font-medium transition-colors duration-200"
          >
            Export
          </Button>
        </div>
      </div>

      {/* Floating Controls Overlay */}
      {showControls && (
        <>
          {/* Backdrop */}
          <div className="absolute inset-0 bg-black/10 dark:bg-black/20 z-40" />

          {/* Controls Panel */}
          <div
            ref={controlsRef}
            className="absolute top-20 left-4 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg overflow-hidden"
          >
            <div className="p-4 space-y-4 max-h-96 overflow-y-auto">
              {/* Search and Filter Row */}
              <div className="flex gap-3 items-end">
                <div className="flex-1">
                  <Input
                    size="sm"
                    placeholder="Search logs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    startContent={<Search className="w-4 h-4" />}
                    className="w-full transition-all duration-200"
                  />
                </div>

                <div className="w-48">
                  <Select
                    size="sm"
                    placeholder="Filter by level"
                    selectedKeys={new Set([selectedLevel])}
                    onSelectionChange={(keys) =>
                      setSelectedLevel(Array.from(keys)[0] as string)
                    }
                    renderValue={renderValue}
                    className="w-full transition-all duration-200"
                  >
                    <SelectItem key="all">All Levels</SelectItem>
                    <SelectItem key="error">
                      Error ({levelCounts.error || 0})
                    </SelectItem>
                    <SelectItem key="warn">
                      Warning ({levelCounts.warn || 0})
                    </SelectItem>
                    <SelectItem key="info">
                      Info ({levelCounts.info || 0})
                    </SelectItem>
                    <SelectItem key="debug">
                      Debug ({levelCounts.debug || 0})
                    </SelectItem>
                  </Select>
                </div>
              </div>

              {/* Entry Limit and Virtual Scroll Controls */}
              <div className="space-y-3">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Display Options
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button
                    size="sm"
                    variant={maxEntries === 100 ? "solid" : "flat"}
                    color={maxEntries === 100 ? "primary" : "default"}
                    onPress={() => setMaxEntries(100)}
                    className="font-medium transition-all duration-200"
                  >
                    Last 100
                  </Button>
                  <Button
                    size="sm"
                    variant={maxEntries === 500 ? "solid" : "flat"}
                    color={maxEntries === 500 ? "primary" : "default"}
                    onPress={() => setMaxEntries(500)}
                    className="font-medium transition-all duration-200"
                  >
                    Last 500
                  </Button>
                  <Button
                    size="sm"
                    variant={maxEntries === 1000 ? "solid" : "flat"}
                    color={maxEntries === 1000 ? "primary" : "default"}
                    onPress={() => setMaxEntries(1000)}
                    className="font-medium transition-all duration-200"
                  >
                    Last 1000
                  </Button>
                  <Button
                    size="sm"
                    variant={maxEntries === Infinity ? "solid" : "flat"}
                    color={maxEntries === Infinity ? "primary" : "default"}
                    onPress={() => setMaxEntries(Infinity)}
                    className="font-medium transition-all duration-200"
                  >
                    Show All
                  </Button>

                  <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1" />

                  <Button
                    size="sm"
                    variant={useVirtualScrolling ? "solid" : "flat"}
                    color={useVirtualScrolling ? "success" : "default"}
                    onPress={() => setUseVirtualScrolling(!useVirtualScrolling)}
                    className="font-medium transition-all duration-200"
                  >
                    Virtual Scroll
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Log Display */}
      <div className="flex-1">
        {filteredLogs.length === 0 ? (
          <div className="bg-gray-200 dark:bg-gray-900 rounded-lg p-2 h-full flex items-center justify-center">
            <div className="text-center text-gray-500 dark:text-gray-400">
              <div className="text-2xl mb-2">📋</div>
              {logs.length === 0 ? (
                <>
                  <p>No logs available yet</p>
                  <p className="text-sm">
                    Logs will appear here when the job starts
                  </p>
                </>
              ) : (
                <>
                  <p>No logs match your filters</p>
                  <p className="text-sm">
                    Try adjusting your search or filter criteria
                  </p>
                </>
              )}
            </div>
          </div>
        ) : useVirtualScrolling ? (
          <div className="h-full">
            <VirtualizedLogList
              logs={filteredLogs}
              searchTerm={searchTerm}
              containerHeight={isFullscreen ? undefined : 280}
              itemHeight={40}
              isFullscreen={isFullscreen}
            />
          </div>
        ) : (
          <div
            ref={logContainerRef}
            className="bg-gray-200 dark:bg-gray-900 rounded-lg p-2 h-full overflow-y-auto"
          >
            <div className="space-y-0.5">
              {filteredLogs.map((entry) => (
                <LogEntryComponent
                  key={entry.id}
                  entry={entry}
                  searchTerm={searchTerm}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
